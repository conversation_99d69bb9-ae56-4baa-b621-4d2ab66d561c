package com.bilibili.miniapp.open.portal.controller.web_api;

import com.bilibili.miniapp.open.common.entity.PageResult;
import com.bilibili.miniapp.open.common.entity.Response;
import com.bilibili.miniapp.open.portal.annotations.MainSiteLoginValidation;
import com.bilibili.miniapp.open.portal.controller.AbstractController;
import com.bilibili.miniapp.open.portal.mapper.SettlementControllerMapper;
import com.bilibili.miniapp.open.portal.vo.common.Context;
import com.bilibili.miniapp.open.portal.vo.settlement.InvoiceUploadResultVo;
import com.bilibili.miniapp.open.portal.vo.settlement.SettlementDateListVo;
import com.bilibili.miniapp.open.portal.vo.settlement.SettlementItemVo;
import com.bilibili.miniapp.open.portal.vo.settlement.SettlementPreviewVo;
import com.bilibili.miniapp.open.service.biz.settlement.ISettlementService;
import com.bilibili.miniapp.open.service.bo.settlement.InvoiceUploadBo;
import com.bilibili.miniapp.open.service.bo.settlement.SettlementDateListBo;
import com.bilibili.miniapp.open.service.bo.settlement.SettlementItemBo;
import com.bilibili.miniapp.open.service.bo.settlement.SettlementPreviewBo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.sql.Timestamp;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 结算相关接口
 *
 * <AUTHOR>
 * @date 2025/1/15
 */
@Slf4j
@RestController
@RequestMapping("/web_api/v1/platform/settlement")
public class SettlementController extends AbstractController {

    @Autowired
    private ISettlementService settlementService;

    /**
     * 获取结算日期列表
     */
    @GetMapping("/date")
    @MainSiteLoginValidation
    public Response<SettlementDateListVo> getSettlementDates(Context context,
                                                             @RequestParam("app_id") String appId) {

        SettlementDateListBo settlementDates = settlementService.getSettlementDates(context.getMid(), appId);

        return Response.SUCCESS(SettlementControllerMapper.MAPPER.boToVo(settlementDates));
    }

    /**
     * 获取结算预览信息
     */
    @GetMapping("/preview")
    @MainSiteLoginValidation
    public Response<SettlementPreviewVo> getSettlementPreview(Context context,
                                                              @RequestParam("app_id") String appId,
                                                              @RequestParam("accrual_ids") List<String> accrualIds) {

        SettlementPreviewBo respBo = settlementService.getSettlementPreview(context.getMid(), appId, accrualIds);

        return Response.SUCCESS(SettlementControllerMapper.MAPPER.boToVo(respBo));
    }

    /**
     * 获取结算列表
     */
    @GetMapping("/list")
    @MainSiteLoginValidation
    public Response<PageResult<SettlementItemVo>> getSettlementList(Context context,
                                                                    @RequestParam(value = "app_id") String appId,
                                                                    @RequestParam(value = "page", defaultValue = "1") Integer page,
                                                                    @RequestParam(value = "size", defaultValue = "20") Integer size,
                                                                    @RequestParam(value = "begin_time", required = false) Timestamp beginTime,
                                                                    @RequestParam(value = "end_time", required = false) Timestamp endTime,
                                                                    @RequestParam(value = "settlement_status", required = false) Integer settlementStatus) {

        PageResult<SettlementItemBo> pageResult = settlementService.getSettlementList(context.getMid(), appId, page, size, beginTime, endTime, settlementStatus);

        List<SettlementItemVo> settlementItemVos = pageResult.getRecords().stream()
                .map(SettlementControllerMapper.MAPPER::boToVo)
                .collect(Collectors.toList());

        PageResult<SettlementItemVo> voPageResult = new PageResult<>(pageResult.getTotal(), settlementItemVos);

        return Response.SUCCESS(voPageResult);
    }

    /**
     * 上传发票文件
     */
    @PostMapping("/invoice/upload")
    @MainSiteLoginValidation
    public Response<InvoiceUploadResultVo> uploadInvoice(@RequestPart("file") MultipartFile file) {

        InvoiceUploadBo uploadResult = settlementService.uploadInvoice(file);

        return Response.SUCCESS(InvoiceUploadResultVo.builder()
                .oid(uploadResult.getOid())
                .url(uploadResult.getUrl())
                .build());
    }
}
